import React, { memo, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Todo } from '@shared/types';
import { TodoItem } from './TodoItem';
import { useVirtualization } from '@renderer/hooks/useVirtualization';
import { staggerContainer, listItemVariants, fadeInUp } from '@renderer/utils/animations';
import { useStaggeredAnimation } from '@renderer/hooks/useAnimations';

export interface VirtualizedTodoListProps {
  todos: Todo[];
  onTodoUpdate: (todo: Todo) => void;
  onTodoDelete: (todoId: string) => void;
  containerHeight: number;
  itemHeight?: number;
  overscan?: number;
  className?: string;
  emptyState?: React.ReactNode;
  loadingState?: React.ReactNode;
  isLoading?: boolean;
}

const DEFAULT_ITEM_HEIGHT = 120; // Approximate height of a TodoItem
const DEFAULT_OVERSCAN = 5;

// Memoized TodoItem wrapper for virtualization
const VirtualizedTodoItem = memo<{
  todo: Todo;
  index: number;
  offsetY: number;
  onUpdate: (todo: Todo) => void;
  onDelete: (todoId: string) => void;
}>(({ todo, index, offsetY, onUpdate, onDelete }) => {
  const { getItemVariants } = useStaggeredAnimation(1);

  return (
    <motion.div
      variants={getItemVariants(index)}
      initial="initial"
      animate="animate"
      exit="exit"
      layout
      style={{
        position: 'absolute',
        top: offsetY,
        left: 0,
        right: 0,
        height: DEFAULT_ITEM_HEIGHT,
        paddingBottom: 12, // Space between items
      }}
    >
      <TodoItem
        todo={todo}
        onUpdate={onUpdate}
        onDelete={onDelete}
      />
    </motion.div>
  );
});

VirtualizedTodoItem.displayName = 'VirtualizedTodoItem';

export const VirtualizedTodoList: React.FC<VirtualizedTodoListProps> = ({
  todos,
  onTodoUpdate,
  onTodoDelete,
  containerHeight,
  itemHeight = DEFAULT_ITEM_HEIGHT,
  overscan = DEFAULT_OVERSCAN,
  className = '',
  emptyState,
  loadingState,
  isLoading = false,
}) => {
  // Use virtualization for performance with large lists
  const shouldVirtualize = todos.length > 50;

  const virtualization = useVirtualization(todos, {
    itemHeight,
    containerHeight,
    overscan,
  });

  // Memoized handlers to prevent unnecessary re-renders
  const handleTodoUpdate = useCallback((updatedTodo: Todo) => {
    onTodoUpdate(updatedTodo);
  }, [onTodoUpdate]);

  const handleTodoDelete = useCallback((todoId: string) => {
    onTodoDelete(todoId);
  }, [onTodoDelete]);

  // Render loading state
  if (isLoading && loadingState) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height: containerHeight }}>
        {loadingState}
      </div>
    );
  }

  // Render empty state
  if (todos.length === 0 && emptyState) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height: containerHeight }}>
        {emptyState}
      </div>
    );
  }

  // Render default empty state
  if (todos.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height: containerHeight }}>
        <div className="text-center">
          <div className="text-5xl mb-4">🍃</div>
          <h3 className="fa-heading-3 mb-2">No tasks found</h3>
          <p className="fa-body text-fa-gray-500">
            Try adjusting your filters or add a new task
          </p>
        </div>
      </div>
    );
  }

  // Render virtualized list for large datasets
  if (shouldVirtualize) {
    return (
      <div className={className}>
        <div {...virtualization.containerProps}>
          <div {...virtualization.contentProps}>
            {virtualization.virtualItems.map(({ item: todo, index, offsetY }) => (
              <VirtualizedTodoItem
                key={todo.id}
                todo={todo}
                index={index}
                offsetY={offsetY}
                onUpdate={handleTodoUpdate}
                onDelete={handleTodoDelete}
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Render regular list for smaller datasets
  const { getItemVariants } = useStaggeredAnimation(todos.length);

  return (
    <motion.div
      className={`overflow-y-auto ${className}`}
      style={{ height: containerHeight }}
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      exit="exit"
    >
      <div className="space-y-3 p-1">
        <AnimatePresence mode="popLayout">
          {todos.map((todo, index) => (
            <motion.div
              key={todo.id}
              variants={getItemVariants(index)}
              initial="initial"
              animate="animate"
              exit="exit"
              layout
            >
              <TodoItem
                todo={todo}
                onUpdate={handleTodoUpdate}
                onDelete={handleTodoDelete}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

// Hook for scroll-to functionality
export const useVirtualizedTodoListControls = (
  todos: Todo[],
  containerHeight: number,
  itemHeight: number = DEFAULT_ITEM_HEIGHT
) => {
  const virtualization = useVirtualization(todos, {
    itemHeight,
    containerHeight,
  });

  const scrollToTodo = useCallback((todoId: string) => {
    const index = todos.findIndex(todo => todo.id === todoId);
    if (index !== -1) {
      virtualization.scrollToIndex(index, 'center');
    }
  }, [todos, virtualization]);

  const scrollToTop = useCallback(() => {
    virtualization.scrollToTop();
  }, [virtualization]);

  const scrollToBottom = useCallback(() => {
    virtualization.scrollToBottom();
  }, [virtualization]);

  return {
    scrollToTodo,
    scrollToTop,
    scrollToBottom,
  };
};

// Performance monitoring component
export const VirtualizedTodoListWithMetrics = memo<VirtualizedTodoListProps & {
  onPerformanceMetrics?: (metrics: {
    renderTime: number;
    itemCount: number;
    visibleItems: number;
  }) => void;
}>(({ onPerformanceMetrics, ...props }) => {
  const startTime = performance.now();

  React.useEffect(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;

    onPerformanceMetrics?.({
      renderTime,
      itemCount: props.todos.length,
      visibleItems: Math.min(props.todos.length, Math.ceil(props.containerHeight / (props.itemHeight || DEFAULT_ITEM_HEIGHT))),
    });
  });

  return <VirtualizedTodoList {...props} />;
});

VirtualizedTodoListWithMetrics.displayName = 'VirtualizedTodoListWithMetrics';
